{"version": 3, "sources": ["webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-card/uni-card.vue?e236", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-card/uni-card.vue?7f75", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-card/uni-card.vue?12ec", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-card/uni-card.vue?663d", "uni-app:///node_modules/@dcloudio/uni-ui/lib/uni-card/uni-card.vue", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-card/uni-card.vue?af3f", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-card/uni-card.vue?bc48"], "names": ["name", "emits", "props", "title", "type", "default", "subTitle", "padding", "margin", "spacing", "extra", "cover", "thumbnail", "isFull", "is<PERSON><PERSON>ow", "shadow", "border", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgyB,CAAgB,gzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsCpzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAkBA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACA;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpHA;AAAA;AAAA;AAAA;AAAm+C,CAAgB,u7CAAG,EAAC,C;;;;;;;;;;;ACAv/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/@dcloudio/uni-ui/lib/uni-card/uni-card.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-card.vue?vue&type=template&id=80554eb4&\"\nvar renderjs\nimport script from \"./uni-card.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-card.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-card/uni-card.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-card.vue?vue&type=template&id=80554eb4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-card.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-card\" :class=\"{ 'uni-card--full': isFull, 'uni-card--shadow': isShadow,'uni-card--border':border}\"\r\n\t\t:style=\"{'margin':isFull?0:margin,'padding':spacing,'box-shadow':isShadow?shadow:''}\">\n\t\t<!-- 封面 -->\r\n\t\t<slot name=\"cover\">\r\n\t\t\t<view v-if=\"cover\" class=\"uni-card__cover\">\r\n\t\t\t\t<image class=\"uni-card__cover-image\" mode=\"widthFix\" @click=\"onClick('cover')\" :src=\"cover\"></image>\r\n\t\t\t</view>\r\n\t\t</slot>\r\n\t\t<slot name=\"title\">\r\n\t\t\t<view v-if=\"title || extra\" class=\"uni-card__header\">\r\n\t\t\t\t<!-- 卡片标题 -->\r\n\t\t\t\t<view class=\"uni-card__header-box\" @click=\"onClick('title')\">\r\n\t\t\t\t\t<view v-if=\"thumbnail\" class=\"uni-card__header-avatar\">\r\n\t\t\t\t\t\t<image class=\"uni-card__header-avatar-image\" :src=\"thumbnail\" mode=\"aspectFit\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"uni-card__header-content\">\r\n\t\t\t\t\t\t<text class=\"uni-card__header-content-title uni-ellipsis\">{{ title }}</text>\r\n\t\t\t\t\t\t<text v-if=\"title&&subTitle\"\r\n\t\t\t\t\t\t\tclass=\"uni-card__header-content-subtitle uni-ellipsis\">{{ subTitle }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-card__header-extra\" @click=\"onClick('extra')\">\r\n\t\t\t\t\t<text class=\"uni-card__header-extra-text\">{{ extra }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</slot>\r\n\t\t<!-- 卡片内容 -->\r\n\t\t<view class=\"uni-card__content\" :style=\"{padding:padding}\" @click=\"onClick('content')\">\r\n\t\t\t<slot></slot>\r\n\t\t</view>\r\n\t\t<view class=\"uni-card__actions\" @click=\"onClick('actions')\">\r\n\t\t\t<slot name=\"actions\"></slot>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * Card 卡片\r\n\t * @description 卡片视图组件\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=22\r\n\t * @property {String} title 标题文字\r\n\t * @property {String} subTitle 副标题\r\n\t * @property {Number} padding 内容内边距\r\n\t * @property {Number} margin 卡片外边距\r\n\t * @property {Number} spacing 卡片内边距\r\n\t * @property {String} extra 标题额外信息\r\n\t * @property {String} cover 封面图（本地路径需要引入）\r\n\t * @property {String} thumbnail 标题左侧缩略图\r\n\t * @property {Boolean} is-full = [true | false] 卡片内容是否通栏，为 true 时将去除padding值\r\n\t * @property {Boolean} is-shadow = [true | false] 卡片内容是否开启阴影\r\n\t * @property {String} shadow 卡片阴影\r\n\t * @property {Boolean} border 卡片边框\r\n\t * @event {Function} click 点击 Card 触发事件\r\n\t */\r\n\texport default {\r\n\t\tname: 'UniCard',\r\n\t\temits: ['click'],\r\n\t\tprops: {\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tsubTitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tpadding: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '10px'\r\n\t\t\t},\r\n\t\t\tmargin: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '15px'\r\n\t\t\t},\r\n\t\t\tspacing: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '0 10px'\r\n\t\t\t},\r\n\t\t\textra: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tcover: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tthumbnail: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tisFull: {\r\n\t\t\t\t// 内容区域是否通栏\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tisShadow: {\r\n\t\t\t\t// 是否开启阴影\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tshadow: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '0px 0px 3px 1px rgba(0, 0, 0, 0.08)'\r\n\t\t\t},\r\n\t\t\tborder: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonClick(type) {\r\n\t\t\t\tthis.$emit('click', type)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t$uni-border-3: #EBEEF5 !default;\r\n\t$uni-shadow-base:0 0px 6px 1px rgba($color: #a5a5a5, $alpha: 0.2) !default;\r\n\t$uni-main-color: #3a3a3a !default;\r\n\t$uni-base-color: #6a6a6a !default;\r\n\t$uni-secondary-color: #909399 !default;\r\n\t$uni-spacing-sm: 8px !default;\r\n\t$uni-border-color:$uni-border-3;\r\n\t$uni-shadow: $uni-shadow-base;\r\n\t$uni-card-title: 15px;\r\n\t$uni-cart-title-color:$uni-main-color;\r\n\t$uni-card-subtitle: 12px;\r\n\t$uni-cart-subtitle-color:$uni-secondary-color;\r\n\t$uni-card-spacing: 10px;\r\n\t$uni-card-content-color: $uni-base-color;\r\n\r\n\t.uni-card {\r\n\t\tmargin: $uni-card-spacing;\r\n\t\tpadding: 0 $uni-spacing-sm;\r\n\t\tborder-radius: 4px;\r\n\t\toverflow: hidden;\r\n\t\tfont-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;\r\n\t\tbackground-color: #fff;\r\n\t\tflex: 1;\r\n\r\n\t\t.uni-card__cover {\r\n\t\t\tposition: relative;\r\n\t\t\tmargin-top: $uni-card-spacing;\n\t\t\tflex-direction: row;\r\n\t\t\toverflow: hidden;\r\n\t\t\tborder-radius: 4px;\n\t\t\t.uni-card__cover-image {\n\t\t\t\tflex: 1;\r\n\t\t\t\t// width: 100%;\n\t\t\t\t/* #ifndef APP-PLUS */\n\t\t\t\tvertical-align: middle;\n\t\t\t\t/* #endif */\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.uni-card__header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tborder-bottom: 1px $uni-border-color solid;\n\t\t\tflex-direction: row;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: $uni-card-spacing;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t.uni-card__header-box {\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tflex-direction: row;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t}\r\n\r\n\t\t\t.uni-card__header-avatar {\r\n\t\t\t\twidth: 40px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tborder-radius: 5px;\r\n\t\t\t\tmargin-right: $uni-card-spacing;\r\n\t\t\t\t.uni-card__header-avatar-image {\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\twidth: 40px;\n\t\t\t\t\theight: 40px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.uni-card__header-content {\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t// height: 40px;\r\n\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t.uni-card__header-content-title {\r\n\t\t\t\t\tfont-size: $uni-card-title;\r\n\t\t\t\t\tcolor: $uni-cart-title-color;\r\n\t\t\t\t\t// line-height: 22px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.uni-card__header-content-subtitle {\r\n\t\t\t\t\tfont-size: $uni-card-subtitle;\r\n\t\t\t\t\tmargin-top: 5px;\r\n\t\t\t\t\tcolor: $uni-cart-subtitle-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.uni-card__header-extra {\r\n\t\t\t\tline-height: 12px;\r\n\r\n\t\t\t\t.uni-card__header-extra-text {\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: $uni-cart-subtitle-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.uni-card__content {\r\n\t\t\tpadding: $uni-card-spacing;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: $uni-card-content-color;\r\n\t\t\tline-height: 22px;\r\n\t\t}\r\n\r\n\t\t.uni-card__actions {\r\n\t\t\tfont-size: 12px;\r\n\t\t}\r\n\t}\r\n\n\t.uni-card--border {\n\t\tborder: 1px solid $uni-border-color;\n\t}\n\r\n\t.uni-card--shadow {\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-shadow: $uni-shadow;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-card--full {\r\n\t\tmargin: 0;\r\n\t\tborder-left-width: 0;\r\n\t\tborder-left-width: 0;\r\n\t\tborder-radius: 0;\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t.uni-card--full:after {\r\n\t\tborder-radius: 0;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.uni-ellipsis {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\toverflow: hidden;\r\n\t\twhite-space: nowrap;\r\n\t\ttext-overflow: ellipsis;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tlines: 1;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-card.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-card.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753665199251\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}