{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/index.vue?a5a6", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/index.vue?f7a9", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/index.vue?a818", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/index.vue?8e0c", "uni-app:///pages/tabbar/patrol/index.vue", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/index.vue?38d4", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/patrol/index.vue?5862"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "index", "taskCount", "doing", "done", "abnormal", "doingList", "doneList", "abnormalList", "computed", "tabColor0", "tabColor1", "tabColor2", "onLoad", "onShow", "ctx", "methods", "init", "title", "content", "showCancel", "success", "url", "showTab", "loadCount", "that", "loadDoing", "offset", "limit", "res", "loadDone", "loadAbnormal", "formatTime", "item", "startPatrol", "viewPatrol", "viewTask"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,aAAa,kUAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1GA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0HjzB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;QACAtB;UACAuB;UACAC;UACAC;UACAC;YACA1B;cACA2B;YACA;UACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAT;QACA;UACAU;QACA;MACA;IACA;IACAC;MACA;MACAX;QAAAY;QAAAC;MAAA;QACA;UACAC;YACAJ;UACA;UACAA;QACA;MACA;IACA;IACAK;MACA;MACAf;QAAAY;QAAAC;MAAA;QACA;UACA;UACA;UACA;UACAH;QACA;MACA;IACA;IACAM;MACA;MACAhB;QAAAY;QAAAC;MAAA;QACA;UACA;UACA;UACA;UACAH;QACA;MACA;IACA;IACAO;MACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACAvC;QACA2B;MACA;IACA;IACAa;MACAxC;QACA2B;MACA;IACA;IACAc;MACAzC;QACA2B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpPA;AAAA;AAAA;AAAA;AAA2nC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA/oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/patrol/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tabbar/patrol/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0da9aa77&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/patrol/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=0da9aa77&\"", "var components\ntry {\n  components = {\n    uniGrid: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-grid/uni-grid\" */ \"@dcloudio/uni-ui/lib/uni-grid/uni-grid.vue\"\n      )\n    },\n    uniGridItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-grid-item/uni-grid-item\" */ \"@dcloudio/uni-ui/lib/uni-grid-item/uni-grid-item.vue\"\n      )\n    },\n    uniBadge: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-badge/uni-badge\" */ \"@dcloudio/uni-ui/lib/uni-badge/uni-badge.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniTag: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag\" */ \"@dcloudio/uni-ui/lib/uni-tag/uni-tag.vue\"\n      )\n    },\n    uniCountdown: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-countdown/uni-countdown\" */ \"@dcloudio/uni-ui/lib/uni-countdown/uni-countdown.vue\"\n      )\n    },\n    watermark: function () {\n      return import(\n        /* webpackChunkName: \"components/watermark/watermark\" */ \"@/components/watermark/watermark.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.index == 0 ? _vm.doingList && _vm.doingList.length : null\n  var l0 =\n    _vm.index == 0 && g0\n      ? _vm.__map(_vm.doingList, function (item, __i0__) {\n          var $orig = _vm.__get_orig(item)\n          var m0 =\n            !(item.willSecond >= 172800) &&\n            !(item.willSecond >= 86400) &&\n            item.willSecond > 0\n              ? parseInt(item.willSecond / 3600)\n              : null\n          var m1 =\n            !(item.willSecond >= 172800) &&\n            !(item.willSecond >= 86400) &&\n            item.willSecond > 0\n              ? parseInt((item.willSecond % 3600) / 60)\n              : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var g1 =\n    !(_vm.index == 0) && _vm.index == 1\n      ? _vm.doneList && _vm.doneList.length\n      : null\n  var g2 =\n    !(_vm.index == 0) && !(_vm.index == 1)\n      ? _vm.abnormalList && _vm.abnormalList.length\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"tab-host\">\r\n\t\t\t<uni-grid :column=\"3\" :showBorder=\"false\" :square=\"false\">\r\n\t\t\t\t<uni-grid-item>\r\n\t\t\t\t\t<view class=\"tool-item\" :class=\"{ act: index == 0 }\" @click=\"showTab(0)\">\r\n\t\t\t\t\t\t<uni-badge size=\"small\" :text=\"taskCount.doing\" absolute=\"rightTop\" type=\"error\">\r\n\t\t\t\t\t\t\t<uni-icons custom-prefix=\"zi\" type=\"zi-task-doing\" size=\"30\" :color=\"tabColor0\"></uni-icons>\r\n\t\t\t\t\t\t</uni-badge>\r\n\t\t\t\t\t\t<text class=\"icon-text\">待巡检</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-grid-item>\r\n\t\t\t\t<uni-grid-item>\r\n\t\t\t\t\t<view class=\"tool-item\" :class=\"{ act: index == 1 }\" @click=\"showTab(1)\">\r\n\t\t\t\t\t\t<uni-badge size=\"small\" :text=\"taskCount.done\" absolute=\"rightTop\" type=\"error\">\r\n\t\t\t\t\t\t\t<uni-icons custom-prefix=\"zi\" type=\"zi-task-done\" size=\"30\" :color=\"tabColor1\"></uni-icons>\r\n\t\t\t\t\t\t</uni-badge>\r\n\t\t\t\t\t\t<text class=\"icon-text\">已巡检</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-grid-item>\r\n\t\t\t\t<uni-grid-item>\r\n\t\t\t\t\t<view class=\"tool-item\" :class=\"{ act: index == 2 }\" @click=\"showTab(2)\">\r\n\t\t\t\t\t\t<uni-badge size=\"small\" :text=\"taskCount.abnormal\" absolute=\"rightTop\" type=\"error\">\r\n\t\t\t\t\t\t\t<uni-icons custom-prefix=\"zi\" type=\"zi-task-warning\" size=\"30\"\r\n\t\t\t\t\t\t\t\t:color=\"tabColor2\"></uni-icons>\r\n\t\t\t\t\t\t</uni-badge>\r\n\t\t\t\t\t\t<text class=\"icon-text\">异常项</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-grid-item>\r\n\t\t\t</uni-grid>\r\n\t\t</view>\r\n\t\t<view class=\"tab-content\">\r\n\t\t\t<view v-if=\"index == 0\" class=\"list-block\">\r\n\t\t\t\t<view v-if=\"doingList && doingList.length\">\r\n\t\t\t\t\t<view v-for=\"item in doingList\" :key=\"id\" class=\"list-item\">\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"caption\">{{ item.planName }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">任务单号：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.no }}</view>\r\n\t\t\t\t\t\t\t<view class=\"time\">\r\n\t\t\t\t\t\t\t\t<uni-tag v-if=\"item.willSecond >= 172800\" circle size=\"small\" text=\"2天以后\"></uni-tag>\r\n\t\t\t\t\t\t\t\t<uni-tag v-else-if=\"item.willSecond >= 86400\" circle size=\"small\" text=\"1天以后\"></uni-tag>\r\n\t\t\t\t\t\t\t\t<uni-countdown v-else-if=\"item.willSecond > 0\" :color=\"'#FFFFFF'\"\r\n\t\t\t\t\t\t\t\t\t:background-color=\"'#CC0000'\" :border-color=\"'#CC0000'\" :show-day=\"false\"\r\n\t\t\t\t\t\t\t\t\t:hour=\"parseInt(item.willSecond / 3600)\"\r\n\t\t\t\t\t\t\t\t\t:minute=\"parseInt((item.willSecond % 3600) / 60)\"\r\n\t\t\t\t\t\t\t\t\t:second=\"item.willSecond % 60\"></uni-countdown>\r\n\t\t\t\t\t\t\t\t<uni-tag v-else-if=\"item.willSecond > -86000\" type=\"warning\" circle size=\"small\"\r\n\t\t\t\t\t\t\t\t\ttext=\"待执行\"></uni-tag>\r\n\t\t\t\t\t\t\t\t<uni-tag v-else type=\"royal\" mark size=\"small\" text=\"已到期\"></uni-tag>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">计划时间：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.timeText }}</view>\r\n\t\t\t\t\t\t\t<view class=\"button\">\r\n\t\t\t\t\t\t\t\t<button v-if=\"item.willSecond < 300\" type=\"primary\" size=\"mini\" :data-id=\"item.id\"\r\n\t\t\t\t\t\t\t\t\t@click=\"startPatrol\">开始巡检</button>\r\n\t\t\t\t\t\t\t\t<button v-else size=\"mini\" :data-id=\"item.id\" @click=\"viewTask\">任务详情</button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-text v-else class=\"zy-empty\">近期未有巡检单</uni-text>\r\n\t\t\t</view>\r\n\t\t\t<view v-else-if=\"index == 1\" class=\"list-block\">\r\n\t\t\t\t<view v-if=\"doneList && doneList.length\">\r\n\t\t\t\t\t<view v-for=\"item in doneList\" :key=\"id\" class=\"list-item\">\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"caption\">{{ item.planName }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">任务单号：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.no }}</view>\r\n\t\t\t\t\t\t\t<view class=\"time\">\r\n\t\t\t\t\t\t\t\t<uni-tag v-if=\"item.result == '2'\" type=\"error\" size=\"small\" text=\"异常\"></uni-tag>\r\n\t\t\t\t\t\t\t\t<uni-tag v-else type=\"success\" mark size=\"small\" text=\"正常\"></uni-tag>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">执行时间：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.execStart }}</view>\r\n\t\t\t\t\t\t\t<view class=\"button\"><button v-if=\"item.willSecond < 300\" size=\"mini\" :data-id=\"item.id\"\r\n\t\t\t\t\t\t\t\t\t@click=\"viewPatrol\">巡检详情</button></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-text v-else class=\"zy-empty\">没有巡检单</uni-text>\r\n\t\t\t</view>\r\n\t\t\t<view v-else>\r\n\t\t\t\t<view v-if=\"abnormalList && abnormalList.length\" class=\"list-block\">\r\n\t\t\t\t\t<view v-for=\"item in abnormalList\" :key=\"id\" class=\"list-item\">\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"caption\">{{ item.planName }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">任务单号：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.no }}</view>\r\n\t\t\t\t\t\t\t<view class=\"time\">\r\n\t\t\t\t\t\t\t\t<uni-tag type=\"error\" size=\"small\" text=\"异常\"></uni-tag>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"row\">\r\n\t\t\t\t\t\t\t<view class=\"label\">执行时间：</view>\r\n\t\t\t\t\t\t\t<view class=\"text\">{{ item.execStart }}</view>\r\n\t\t\t\t\t\t\t<view class=\"button\"><button v-if=\"item.willSecond < 300\" size=\"mini\" :data-id=\"item.id\"\r\n\t\t\t\t\t\t\t\t\t@click=\"viewPatrol\">巡检详情</button></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-text v-else class=\"zy-empty\">未有异常事项</uni-text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 水印组件 -->\r\n\t\t<watermark />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport * as ctx from '../../../utils/context.js'\r\nimport { formatTimeRange } from '../../../utils/timeUtils.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tindex: 0,\r\n\t\t\ttaskCount: {\r\n\t\t\t\tdoing: 0,\r\n\t\t\t\tdone: 0,\r\n\t\t\t\tabnormal: 0\r\n\t\t\t},\r\n\t\t\tdoingList: [],\r\n\t\t\tdoneList: [],\r\n\t\t\tabnormalList: [],\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\ttabColor0: function () {\r\n\t\t\treturn this.index == 0 ? '#007AFF' : '#888888'\r\n\t\t},\r\n\t\ttabColor1: function () {\r\n\t\t\treturn this.index == 1 ? '#007AFF' : '#888888'\r\n\t\t},\r\n\t\ttabColor2: function () {\r\n\t\t\treturn this.index == 2 ? '#007AFF' : '#888888'\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\r\n\t},\r\n\tonShow: function () {\r\n\t\tctx.checkLogin(this.init, true, true);\r\n\t},\r\n\tmethods: {\r\n\t\tinit(user) {\r\n\t\t\tif (user == null || user.user == '0') {\r\n\t\t\t\twx.showModal({\r\n\t\t\t\t\ttitle: '系统提示',\r\n\t\t\t\t\tcontent: '您还未绑定，请先进行绑定',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\twx.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/mine/index',\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.loadCount()\r\n\t\t\tthis.loadDoing()\r\n\t\t\tthis.loadDone()\r\n\t\t\tthis.loadAbnormal()\r\n\t\t},\r\n\t\tshowTab(index) {\r\n\t\t\tthis.index = index\r\n\t\t},\r\n\t\tloadCount() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/patrol/task/count', function (res) {\r\n\t\t\t\tif (res.code > 0) {\r\n\t\t\t\t\tthat.taskCount = res.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tloadDoing() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/patrol/task/doing', { offset: 0, limit: 50 }, function (res) {\r\n\t\t\t\tif (res.code > 0) {\r\n\t\t\t\t\tres.data.forEach(r => {\r\n\t\t\t\t\t\tthat.formatTime(r)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.doingList = res.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tloadDone() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/patrol/task/done', { offset: 0, limit: 50 }, function (res) {\r\n\t\t\t\tif (res.code > 0) {\r\n\t\t\t\t\t// res.data.forEach(r => {\r\n\t\t\t\t\t// \tthat.formatExecTime(r)\r\n\t\t\t\t\t// })\r\n\t\t\t\t\tthat.doneList = res.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tloadAbnormal() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/patrol/task/abnormal', { offset: 0, limit: 50 }, function (res) {\r\n\t\t\t\tif (res.code > 0) {\r\n\t\t\t\t\t// res.data.forEach(r => {\r\n\t\t\t\t\t// \tthat.formatExecTime(r)\r\n\t\t\t\t\t// })\r\n\t\t\t\t\tthat.abnormalList = res.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tformatTime(item) {\r\n\t\t\titem.timeText = formatTimeRange(item)\r\n\t\t},\r\n\t\t// formatExecTime(item) {\r\n\t\t// \tconst st = item.execStart ? item.execStart.replace(/:00$/, '') : ''\r\n\t\t// \tconst et = item.execEnd ? item.execEnd.replace(/(^.*\\s)|(:00$)/g, '') : ''\r\n\t\t// \titem.timeExec = (st.length < 12 ? st + ' 00:00' : st) + '~' + et\r\n\t\t// },\r\n\t\tstartPatrol(e) {\r\n\t\t\twx.navigateTo({\r\n\t\t\t\turl: 'detail?id=' + e.currentTarget.dataset.id,\r\n\t\t\t})\r\n\t\t},\r\n\t\tviewPatrol(e) {\r\n\t\t\twx.navigateTo({\r\n\t\t\t\turl: 'view?id=' + e.currentTarget.dataset.id,\r\n\t\t\t})\r\n\t\t},\r\n\t\tviewTask(e) {\r\n\t\t\twx.navigateTo({\r\n\t\t\t\turl: 'task?id=' + e.currentTarget.dataset.id,\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {}\r\n\r\n.tab-host {\r\n\ttext-align: center;\r\n\tborder-bottom: 1px solid #ccc;\r\n\tpadding-bottom: 10rpx;\r\n\tpadding-top: 20rpx;\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tz-index: 100;\r\n\twidth: 100%;\r\n\tbackground-color: #FFFFFF;\r\n}\r\n\r\n.tab-content {\r\n\tmargin-top: 150rpx;\r\n}\r\n\r\n.tool-item uni-icons {\r\n\tdisplay: block;\r\n}\r\n\r\n.tool-item .icon-text {\r\n\tdisplay: block;\r\n\tline-height: 60rpx;\r\n}\r\n\r\n.tool-item.act .icon-text {\r\n\tcolor: #007AFF;\r\n}\r\n\r\n.list-block {\r\n\tbackground-color: #EEE;\r\n\tpadding: 0;\r\n}\r\n\r\n.list-item {\r\n\tmargin-top: 1px;\r\n\tpadding: 8px;\r\n\tbackground-color: #FFF;\r\n}\r\n\r\n.list-item .row {\r\n\tmargin: 4px 0;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n}\r\n\r\n.list-item .row .caption {\r\n\tflex: 1;\r\n\tfont-size: 14px;\r\n\tfont-weight: bold;\r\n\tline-height: 40px;\r\n\toverflow: hidden;\r\n\ttext-align: left;\r\n}\r\n\r\n.list-item .row .button {\r\n\tmin-width: 80px;\r\n\twhite-space: nowrap;\r\n\ttext-align: right;\r\n}\r\n\r\n.list-item .row .label {\r\n\tfont-size: 12px;\r\n\tmin-width: 60px;\r\n\twhite-space: nowrap;\r\n\tline-height: 24px;\r\n}\r\n\r\n.list-item .row .text {\r\n\tflex: 1;\r\n\tfont-size: 12px;\r\n\tcolor: #666;\r\n\tline-height: 24px;\r\n}\r\n\r\n.list-item .row .time {\r\n\tfont-size: 12px;\r\n\tmin-width: 80px;\r\n\twhite-space: nowrap;\r\n\ttext-align: right;\r\n}\r\n\r\n.time-item .van-count-down {\r\n\tmargin: 2px 4px;\r\n\tcolor: #FFF;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753665194914\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}