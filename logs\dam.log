07-28 10:08:26.701 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-28 10:08:26.706 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 38296 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-28 10:08:26.707 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-28 10:08:27.461 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-28 10:08:27.463 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-28 10:08:27.486 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
07-28 10:08:27.655 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=1b44a730-e903-3697-99d9-e7814c6b20b7
07-28 10:08:27.933 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-28 10:08:27.940 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-28 10:08:27.940 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-28 10:08:27.940 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-28 10:08:28.094 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-28 10:08:28.095 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1353 ms
07-28 10:08:28.124 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-28 10:08:28.180 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-28 10:08:28.706 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-28 10:08:30.379 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-28 10:08:30.386 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-28 10:08:30.386 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-28 10:08:30.386 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-28 10:08:30.387 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-28 10:08:30.387 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-28 10:08:30.387 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-28 10:08:30.387 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3458be63
07-28 10:08:31.586 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
07-28 10:08:31.592 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-28 10:08:31.636 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-28 10:08:31.676 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-28 10:08:31.693 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-28 10:08:32.882 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
07-28 10:08:32.882 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-28 10:08:32.882 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-28 10:08:32.891 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.72 seconds (JVM running for 8.695)
