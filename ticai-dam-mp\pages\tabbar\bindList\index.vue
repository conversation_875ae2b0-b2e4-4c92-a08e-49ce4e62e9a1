<template>
	<view class="container">
		<view v-if="index == 0" class="list-block">
			<!-- 搜索框和筛选条件 -->
			<view class="search-container">
				<view class="search-input-wrapper">
					<uni-easyinput type="text" v-model="keyword" placeholder="请输入姓名,账号,编码,终端号,网点备注查询" style="height: 70rpx;"
						confirm-type="search" @confirm="searchNo" />
				</view>
				<view class="search-buttons">
					<button type="primary" class="icon-btn" plain="true" @click="searchNo">
						<uni-icons type="search" size="20"></uni-icons>
					</button>
					<button type="primary" class="icon-btn" plain="true" @click="assetScan">
						<uni-icons type="scan" size="20"></uni-icons>
					</button>
				</view>
			</view>

			<!-- 折叠面板 -->
			<uni-collapse v-model="collapseValue" @change="onCollapseChange"
				:class="{ 'date-picker-expanded': datePickerVisible }">
				<uni-collapse-item :title="collapseTitle" :show-animation="true" name="filter">
					<view class="filter-content" :class="{ 'date-picker-expanded': datePickerVisible }">
						<!-- 是否领用 -->
						<view class="filter-row">
							<text class="filter-label">是否领用：</text>
							<uni-data-checkbox v-model="filterConditions.isReceived" :localdata="receivedOptions"
								mode="button" @change="onReceivedChange" />
						</view>

						<!-- 是否绑定 -->
						<view class="filter-row">
							<text class="filter-label">是否绑定：</text>
							<uni-data-checkbox v-model="filterConditions.isBound" :localdata="boundOptions"
								mode="button" @change="onBoundChange" />
						</view>

						<!-- 是否定位 -->
						<view class="filter-row">
							<text class="filter-label">是否定位：</text>
							<uni-data-checkbox v-model="filterConditions.isLocated" :localdata="locatedOptions"
								mode="button" @change="onLocatedChange" />
						</view>

						<!-- 网点日期 -->
						<view class="filter-row">
							<text class="filter-label">网点日期：</text>
							<view class="date-picker-wrapper" @click="onDatePickerClick">
								<uni-datetime-picker v-model="filterConditions.dateRange" type="daterange"
									@change="onDateRangeChange" @input="onDateRangeInput"
									@maskClick="onDatePickerMaskClick" @clear="onDateRangeClear"
									@confirm="onDateRangeConfirm" @cancel="onDateRangeCancel" :placeholder="'请选择日期范围'"
									clearIcon />
							</view>
						</view>
					</view>
				</uni-collapse-item>
			</uni-collapse>

			<!-- 数据列表区域 -->
			<view v-if="doList && doList.length">
				<view v-for="item in doList" :key="id" class="list-item">
					<!-- 数据类型标签 -->
					<!-- <view class="data-type-tag" :class="'type-' + item.dataType">
						{{ getDataTypeText(item.dataType) }}
					</view> -->

					<!-- 所有类型数据统一显示所有字段 -->
					<view class="row">
						<view class="label">绑定姓名：</view>
						<view class="text">{{ item.name || '-' }}</view>
						<view class="time">{{ formatTime(item.bindTime) }}</view>
					</view>
					<view class="row">
						<view class="label">绑定账号：</view>
						<view class="text">{{ item.account || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">资产编码：</view>
						<view class="text">{{ item.no || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">终端号：</view>
						<view class="text">{{ item.nowSn || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">规则型号：</view>
						<view class="text">{{ item.spec || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">资产类型：</view>
						<view class="text">{{ item.typeName || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">资产名称：</view>
						<view class="text">{{ item.assetName || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">业主名称：</view>
						<view class="text">{{ item.locationName || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">网点日期：</view>
						<view class="text">{{ formatTime(item.locationTime) || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">网点地址：</view>
						<view class="text">{{ item.locationAddress || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">网点备注：</view>
						<view class="text">{{ item.locationMemo || '-' }}</view>
					</view>
					<view class="row">
						<view class="label">定位地址：</view>
						<view class="text">{{ item.locAddr || '-' }}</view>
					</view>
					<view class="row row-with-divider">
						<view class="label">是否定位：</view>
						<view class="text">{{ item.whetherLocation || '-' }}</view>
					</view>
				</view>
			</view>

			<!-- 加载状态提示 -->
			<uni-load-more :status="moreStatus" :contentText="contentText">
			</uni-load-more>
		</view>
	</view>
</template>

<script>
import * as ctx from '../../../utils/context.js'
import { formatTime } from '../../../utils/timeUtils.js'

export default {
	data() {
		return {
			index: 0,
			doList: [],
			keyword: null,
			pageSize: 10,
			pageNo: 1,
			moreStatus: 'more',
			contentText: {
				contentdown: '上拉加载更多',
				contentrefresh: '加载中',
				contentnomore: '没有更多'
			},
			list: [],
			// 折叠面板控制
			collapseValue: [],
			// 日期选择器状态控制
			datePickerVisible: false,
			// 筛选条件
			filterConditions: {
				isReceived: '2', // 是否领用，默认选择全部
				isLocated: '2', // 是否定位，默认选择全部
				isBound: '2', // 是否绑定，默认选择全部
				dateRange: null // 网点日期范围
			},
			// 选项数据
			receivedOptions: [
				{ text: '全部', value: '2' },
				{ text: '已领用', value: '1' },
				{ text: '未领用', value: '0' }
			],
			locatedOptions: [
				{ text: '全部', value: '2' },
				{ text: '已定位', value: '1' },
				{ text: '未定位', value: '0' }
			],
			boundOptions: [
				{ text: '全部', value: '2' },
				{ text: '已绑定', value: '1' },
				{ text: '未绑定', value: '0' }
			],
			// 标记是否来自扫码定位
			fromScanLocation: false
		}
	},
	computed: {
		// 根据折叠面板状态动态显示标题
		collapseTitle() {
			return this.collapseValue && this.collapseValue.includes('filter')
				? '点击折叠筛选条件'
				: '点击展开更多筛选条件';
		}
	},
	onLoad: function (options) {
		// 检查是否有终端号参数
		if (options && options.terminalNo) {
			this.keyword = decodeURIComponent(options.terminalNo);
			// 标记来自扫码定位，需要自动查询
			this.fromScanLocation = true;
		}
	},
	onShow: function () {
		ctx.checkLogin(this.init, false, true);
	},
	onReachBottom() {
		this.doQuery();
	},
	watch: {
		'filterConditions.dateRange': {
			handler(newVal, oldVal) {
				// 如果从有值变为空值，触发查询
				if (oldVal && (newVal === null || newVal === undefined || newVal === '' ||
					(Array.isArray(newVal) && newVal.length === 0))) {
					this.$nextTick(() => {
						this.pageNo = 1;
						this.doList = [];
						this.doQuery();
					});
				}
			},
			deep: true
		}
	},

	methods: {
		init(user) {
			if (user == null || user.user == '0') {
				wx.showModal({
					title: '系统提示',
					content: '您还未绑定，请先进行绑定',
					showCancel: false,
					success: () => {
						wx.navigateTo({
							url: '/pages/mine/index',
						})
					}
				})
				return
			}

			// 如果来自扫码定位且有终端号，自动执行查询
			if (this.fromScanLocation && this.keyword) {
				this.doQuery()
			} else {
				this.doQuery()
			}
		},

		getDataTypeText(dataType) {
			switch (dataType) {
				case 1: return '已领用已绑定';
				case 2: return '已领用未绑定';
				case 3: return '未绑定网点';
				default: return '';
			}
		},
		loadDoing() {
			const that = this
			ctx.post('/wx/getAssetBindingList', function (r) {
				if (r.code > 0) {
					r.data.forEach(r => {
						that.doList.push(r)
					})
				}
			})
		},
		searchNo() {
			this.pageNo = 1;
			this.doList = []; // 清空当前列表
			this.doQuery();
		},
		assetScan() {
			const that = this;
			ctx.checkLogin(() => {
				wx.scanCode({
					onlyFromCamera: true,
					scanType: 'qrCode',
					success: function (res) {
						const qr = res.result;
						if (qr) {
							// 验证二维码格式：1:id\n...
							if (qr.substring(0, 2) !== '1:') {
								return ctx.error('无效的二维码');
							}

							// 解析二维码内容，格式：1:id\ntype\nno\nname\nsn\nbrand\nspec\ntakeDate\nvalue
							const content = qr.substring(2); // 去掉 "1:" 前缀
							const lines = content.split('\n');
							let searchKeyword = '';

							// 优先获取终端号(sn) - 位置在第2个字段（索引2）
							if (lines.length >= 3 && lines[2] && lines[2].trim()) {
								searchKeyword = lines[2].trim();
							}
							// 如果没有终端号，获取编码(no) - 位置在第1个字段（索引1）
							else if (lines.length >= 2 && lines[1] && lines[1].trim()) {
								searchKeyword = lines[1].trim();
							}

							if (searchKeyword) {
								// 设置搜索关键字并执行查询
								that.keyword = searchKeyword;
								that.pageNo = 1;
								that.doList = []; // 清空当前列表
								that.doQuery(); // 调用分页查询接口进行筛选
							} else {
								ctx.error('二维码中未找到有效的终端号或编码');
							}
						}
					},
					fail: function (err) {
						console.log('扫码失败:', err);
					}
				});
			}, false);
		},
		doQuery() {
			const that = this;
			let params = {};
			params['pageSize'] = this.pageSize;
			params['pageNumber'] = this.pageNo;
			params['keyword'] = this.keyword;

			// 添加筛选条件
			if (this.filterConditions.isReceived && this.filterConditions.isReceived !== '2') {
				params['isReceived'] = this.filterConditions.isReceived === '0' ? 'no' : 'yes';
			}
			if (this.filterConditions.isLocated && this.filterConditions.isLocated !== '2') {
				params['isLocated'] = this.filterConditions.isLocated === '0' ? 'no' : 'yes';
			}
			if (this.filterConditions.isBound && this.filterConditions.isBound !== '2') {
				params['isBound'] = this.filterConditions.isBound === '0' ? 'no' : 'yes';
			}

			// 处理时间范围筛选条件
			// 只有当dateRange是有效的数组且包含两个非空日期时才添加时间参数
			if (this.filterConditions.dateRange !== null &&
				this.filterConditions.dateRange !== undefined &&
				this.filterConditions.dateRange !== '' &&
				Array.isArray(this.filterConditions.dateRange) &&
				this.filterConditions.dateRange.length === 2 &&
				this.filterConditions.dateRange[0] &&
				this.filterConditions.dateRange[1] &&
				this.filterConditions.dateRange[0].trim() !== '' &&
				this.filterConditions.dateRange[1].trim() !== '') {
				// 有时间范围时添加时间参数
				params['dateStart'] = this.filterConditions.dateRange[0];
				params['dateEnd'] = this.filterConditions.dateRange[1];
			}

			that.moreStatus = 'loading'
			ctx.post('/am/asset/getAmAssetPage', params, function (res) {
				if (!res || !res.rows) {
					that.moreStatus = 'noMore';
					return;
				}

				let currentPageNo = params.pageNumber;
				let total = res.total;
				let pages = parseInt((total / that.pageSize)) + (total % that.pageSize === 0 ? 0 : 1);

				if (pages <= currentPageNo) {
					that.moreStatus = 'noMore';
				} else {
					that.moreStatus = 'more';
				}

				// 处理数据
				if (currentPageNo === 1) {
					that.doList = res.rows;
				} else {
					that.doList = that.doList.concat(res.rows);
				}

				// 只有在成功获取数据后才递增页码
				that.pageNo++;
			})
		},
		// 是否领用变化
		onReceivedChange() {
			this.pageNo = 1;
			this.doList = [];
			this.doQuery();
		},
		// 是否定位变化
		onLocatedChange() {
			this.pageNo = 1;
			this.doList = [];
			this.doQuery();
		},
		// 是否绑定变化
		onBoundChange() {
			this.pageNo = 1;
			this.doList = [];
			this.doQuery();
		},
		// 日期选择器点击
		onDatePickerClick(event) {
			// 阻止事件冒泡，防止触发折叠面板的点击事件
			if (event && event.stopPropagation) {
				event.stopPropagation();
			}
			// 短暂延时后设置为展开状态，给日期选择器时间渲染
			setTimeout(() => {
				this.datePickerVisible = true;
			}, 100);
		},
		// 日期选择器遮罩点击
		onDatePickerMaskClick() {
			this.datePickerVisible = false;
		},
		// 日期范围变化
		onDateRangeChange() {
			// 如果日期范围被清空，确保重置为null
			if (!this.filterConditions.dateRange ||
				this.filterConditions.dateRange === '' ||
				(Array.isArray(this.filterConditions.dateRange) && this.filterConditions.dateRange.length === 0)) {
				this.filterConditions.dateRange = null;
			}

			this.pageNo = 1;
			this.doList = [];
			this.datePickerVisible = false; // 选择完成后关闭展开状态
			this.doQuery();
		},
		// 日期范围输入变化
		onDateRangeInput() {
			// 这个事件可能在清空时也会触发
		},
		// 日期范围确认
		onDateRangeConfirm() {
			// 日期确认事件
		},
		// 日期范围取消
		onDateRangeCancel() {
			// 日期取消事件
		},
		// 日期范围清空
		onDateRangeClear() {
			this.filterConditions.dateRange = null;
			this.pageNo = 1;
			this.doList = [];
			this.doQuery();
		},
		// 折叠面板变化
		onCollapseChange(e) {
			this.collapseValue = e;
			// 当折叠面板关闭时，同时关闭日期选择器的展开状态
			if (!e || e.length === 0) {
				this.datePickerVisible = false;
			}
		},
		// 格式化时间
		formatTime
	}
}
</script>

<style scoped>
.container {
	margin: 10rpx;
}

.list-block {
	padding: 0;
}

.list-item {
	margin-top: 1px;
	padding: 8px;
	background-color: #FFF;
	border-bottom: 1px solid #ffffff;
}

.list-item .row {
	margin: 4px 0;
	display: flex;
	flex-direction: row;
	align-items: center;
}

.list-item .row .label {
	font-size: 12px;
	min-width: 60px;
	white-space: nowrap;
	line-height: 24px;
}

.list-item .row .text {
	flex: 1;
	font-size: 12px;
	color: #666;
	line-height: 24px;
}

.list-item .row .time {
	font-size: 12px;
	min-width: 80px;
	white-space: nowrap;
	text-align: right;
}

/* 添加分割线 */
.list-item .row.row-with-divider {
	border-bottom: 1px solid #e5e5e5;
	padding-bottom: 15px;
}

/* 搜索容器样式 */
.search-container {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.search-input-wrapper {
	flex: 1;
	min-width: 0;
	/* 确保可以收缩 */
}

.search-buttons {
	display: flex;
	gap: 8rpx;
	flex-shrink: 0;
	/* 防止按钮被压缩 */
}

.icon-btn {
	height: 70rpx;
	width: 70rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	margin: 0;
	background-color: #ffffff !important;
	border-color: #e5e5e5 !important;
}

.data-type-tag {
	display: inline-block;
	padding: 2px 8px;
	border-radius: 10px;
	font-size: 12px;
	color: #fff;
	margin-bottom: 8px;
}

.type-1 {
	background-color: #67C23A;
	/* 绿色 - 已领用已绑定 */
}

.type-2 {
	background-color: #E6A23C;
	/* 黄色 - 已领用未绑定 */
}

.type-3 {
	background-color: #909399;
	/* 灰色 - 未绑定网点 */
}

/* 筛选面板样式 */
.filter-content {
	padding: 20rpx 20rpx 20rpx 0rpx;
	background-color: #ffffff;
}

.filter-row {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
	position: relative;
}

.filter-label {
	font-size: 12px;
	color: #333;
	min-width: 120rpx;
	margin-right: 20rpx;
	margin-left: 0rpx;
}

/* 折叠面板标题样式*/
::v-deep .uni-collapse-item__title-text {
	font-size: 12px !important;
}

/* 日期选择器样式 */
.date-picker-wrapper {
	flex: 1;
	position: relative;
	z-index: 1;
}

/* 日期选择器输入框样式 */
::v-deep .uni-datetime-picker {
	height: 60rpx !important;
}

::v-deep .uni-datetime-picker .uni-datetime-picker-text {
	font-size: 12px !important;
	height: 60rpx !important;
	line-height: 60rpx !important;
}

::v-deep .uni-datetime-picker .uni-datetime-picker-view {
	height: 60rpx !important;
	line-height: 60rpx !important;
}

/* 日期选择器内部开始日期和结束日期文字样式 */
::v-deep .uni-date__x-input {
	font-size: 12px;
}

/* 确保筛选内容区域不限制弹出层 - 只在展开时生效 */
.filter-content {
	position: relative !important;
	padding: 2px 15px;
}

/* 去掉选项数据外层长方形边框 */
::v-deep .uni-data-checkbox {
	border: none !important;
	background: transparent !important;
}

::v-deep .checklist-group {
	border: none !important;
	background: transparent !important;
	box-shadow: none !important;
}

::v-deep .checklist-box {
	border: none !important;
	background: transparent !important;
	box-shadow: none !important;
}

::v-deep .checklist-content {
	border: none !important;
	background: transparent !important;
}

/* 保留单选框圆形边框 */
::v-deep .radio__inner {
	border: 1px solid #d9d9d9 !important;
}

::v-deep .radio__inner.radio--checked {
	border-color: #007aff !important;
}

/* 按钮模式下的样式优化 */
::v-deep .uni-data-checklist .checklist-group .checklist-box.is--button {
	margin-right: 10rpx !important;
	margin-left: 8rpx !important;
	padding: 8rpx 16rpx !important;
	transition: all 0.2s ease !important;
}

::v-deep .uni-data-checklist .checklist-group .checklist-box.is--button .checklist-text {
	color: #666 !important;
	margin: 0 !important;
	font-size: 12px !important;
	margin-left: 8rpx !important;
}

/* 按钮模式下的选中状态样式 */
::v-deep .uni-data-checklist .checklist-group .checklist-box.is--button.is-checked {
	border-color: #2979ff !important;
}

/* 日期选择器弹出层样式 - 相对定位，在网点日期下方显示 */
::v-deep .uni-datetime-picker-popup,
::v-deep .uni-popper,
::v-deep .uni-datetime-picker-view,
::v-deep .uni-datetime-picker__container,
::v-deep .uni-datetime-picker-popup-view {
	z-index: 999 !important;
	position: absolute !important;
	top: 100% !important;
	left: 0 !important;
	right: 0 !important;
}

/* 日期选择器遮罩层 - 透明，不遮挡其他内容 */
::v-deep .uni-datetime-picker-mask {
	z-index: 998 !important;
	position: absolute !important;
	background-color: transparent !important;
}

/* 日期选择器展开时的折叠面板样式 - 只设置溢出可见，不设置固定高度 */
.date-picker-expanded {
	overflow: visible !important;
}

/* 日期选择器展开时的折叠面板内容样式 */
.date-picker-expanded.filter-content {
	overflow: visible !important;
	position: relative !important;
}

/* 日期选择器展开时的折叠面板项样式 */
::v-deep .date-picker-expanded .uni-collapse-item__content {
	overflow: visible !important;
}

::v-deep .date-picker-expanded .uni-collapse-item__wrap {
	overflow: visible !important;
}

/* 确保日期选择器在小程序中的弹出层样式 - 相对定位在网点日期下方 */
::v-deep .uni-calendar--fixed,
::v-deep .uni-calendar,
::v-deep .uni-date-single--x,
::v-deep .uni-date-range--x {
	z-index: 999 !important;
	position: absolute !important;
	top: 100% !important;
	left: 0 !important;
	right: 0 !important;
	transform: none !important;
}

/* 小程序日历组件特殊处理 */
::v-deep .uni-calendar--fixed {
	position: absolute !important;
	bottom: auto !important;
	top: 100% !important;
	transform: translateY(0) !important;
}
</style>
