{"version": 3, "sources": ["webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag.vue?62eb", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag.vue?438c", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag.vue?b028", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag.vue?8de4", "uni-app:///node_modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag.vue", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag.vue?3092", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag.vue?d721"], "names": ["name", "emits", "props", "type", "default", "size", "text", "disabled", "inverted", "circle", "mark", "customStyle", "computed", "classes", "isTrue", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+xB,CAAgB,+yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACKnzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA,eAqBA;EACAA;EACAC;EACAC;IACAC;MACA;MACAA;MACAC;IACA;IACAC;MACA;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACA;MACAJ;MACAC;IACA;IACAI;MACA;MACAL;MACAC;IACA;IACAK;MACA;MACAN;MACAC;IACA;IACAM;MACA;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;EACA;EACAQ;IACAC;MACA,IACAV,OAOA,KAPAA;QACAI,WAMA,KANAA;QACAC,WAKA,KALAA;QACAC,SAIA,KAJAA;QACAC,OAGA,KAHAA;QACAL,OAEA,KAFAA;QACAS,SACA,KADAA;MAEA,gBACA,oBACA,oBACAA,6CACAA,2DACAA,yCACAA;MACA;MACAA,mEACAT,8CACA;MACA;MACA;IACA;EACA;EACAU;IACAD;MACA;IACA;IACAE;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAA0/C,CAAgB,88CAAG,EAAC,C;;;;;;;;;;;ACA9gD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-tag.vue?vue&type=template&id=ea31adfc&scoped=true&\"\nvar renderjs\nimport script from \"./uni-tag.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-tag.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-tag.vue?vue&type=style&index=0&id=ea31adfc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ea31adfc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-tag/uni-tag.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-tag.vue?vue&type=template&id=ea31adfc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-tag.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-tag.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<text class=\"uni-tag\" v-if=\"text\" :class=\"classes\" :style=\"customStyle\" @click=\"onClick\">{{text}}</text>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * Tag 标签\r\n\t * @description 用于展示1个或多个文字标签，可点击切换选中、不选中的状态\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=35\r\n\t * @property {String} text 标签内容\r\n\t * @property {String} size = [default|small|mini] 大小尺寸\r\n\t * \t@value default 正常\r\n\t * \t@value small 小尺寸\r\n\t * \t@value mini 迷你尺寸\r\n\t * @property {String} type = [default|primary|success｜warning｜error]  颜色类型\r\n\t * \t@value default 灰色\r\n\t * \t@value primary 蓝色\r\n\t * \t@value success 绿色\r\n\t * \t@value warning 黄色\r\n\t * \t@value error 红色\r\n\t * @property {Boolean} disabled = [true|false] 是否为禁用状态\r\n\t * @property {Boolean} inverted = [true|false] 是否无需背景颜色（空心标签）\r\n\t * @property {Boolean} circle = [true|false] 是否为圆角\r\n\t * @event {Function} click 点击 Tag 触发事件\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: \"UniTag\",\r\n\t\temits: ['click'],\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\t// 标签类型default、primary、success、warning、error、royal\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"default\"\r\n\t\t\t},\r\n\t\t\tsize: {\r\n\t\t\t\t// 标签大小 normal, small\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"normal\"\r\n\t\t\t},\r\n\t\t\t// 标签内容\r\n\t\t\ttext: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tdisabled: {\r\n\t\t\t\t// 是否为禁用状态\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tinverted: {\r\n\t\t\t\t// 是否为空心\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tcircle: {\r\n\t\t\t\t// 是否为圆角样式\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tmark: {\r\n\t\t\t\t// 是否为标记样式\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tcustomStyle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tclasses() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\ttype,\r\n\t\t\t\t\tdisabled,\r\n\t\t\t\t\tinverted,\r\n\t\t\t\t\tcircle,\r\n\t\t\t\t\tmark,\r\n\t\t\t\t\tsize,\r\n\t\t\t\t\tisTrue\r\n\t\t\t\t} = this\r\n\t\t\t\tconst classArr = [\r\n\t\t\t\t\t'uni-tag--' + type,\r\n\t\t\t\t\t'uni-tag--' + size,\r\n\t\t\t\t\tisTrue(disabled) ? 'uni-tag--disabled' : '',\r\n\t\t\t\t\tisTrue(inverted) ? 'uni-tag--' + type + '--inverted' : '',\r\n\t\t\t\t\tisTrue(circle) ? 'uni-tag--circle' : '',\r\n\t\t\t\t\tisTrue(mark) ? 'uni-tag--mark' : '',\r\n\t\t\t\t\t// type === 'default' ? 'uni-tag--default' : 'uni-tag-text',\r\n\t\t\t\t\tisTrue(inverted) ? 'uni-tag--inverted uni-tag-text--' + type : '',\r\n\t\t\t\t\tsize === 'small' ? 'uni-tag-text--small' : ''\r\n\t\t\t\t]\r\n\t\t\t\t// 返回类的字符串，兼容字节小程序\r\n\t\t\t\treturn classArr.join(' ')\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tisTrue(value) {\r\n\t\t\t\treturn value === true || value === 'true'\r\n\t\t\t},\r\n\t\t\tonClick() {\r\n\t\t\t\tif (this.isTrue(this.disabled)) return\r\n\t\t\t\tthis.$emit(\"click\");\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t$uni-primary: #2979ff !default;\r\n\t$uni-success: #18bc37 !default;\r\n\t$uni-warning: #f3a73f !default;\r\n\t$uni-error: #e43d33 !default;\r\n\t$uni-info: #8f939c !default;\r\n\r\n\r\n\t$tag-default-pd: 4px 7px;\r\n\t$tag-small-pd: 2px 5px;\r\n\t$tag-mini-pd: 1px 3px;\r\n\r\n\t.uni-tag {\r\n\t\tline-height: 14px;\r\n\t\tfont-size: 12px;\r\n\t\tfont-weight: 200;\r\n\t\tpadding: $tag-default-pd;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 3px;\r\n\t\tbackground-color: $uni-info;\r\n\t\tborder-width: 1px;\r\n\t\tborder-style: solid;\r\n\t\tborder-color: $uni-info;\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\r\n\t\t// size attr\r\n\t\t&--default {\r\n\t\t\tfont-size: 12px;\r\n\t\t}\r\n\r\n\t\t&--default--inverted {\r\n\t\t\tcolor: $uni-info;\r\n\t\t\tborder-color: $uni-info;\r\n\t\t}\r\n\r\n\t\t&--small {\r\n\t\t\tpadding: $tag-small-pd;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tborder-radius: 2px;\r\n\t\t}\r\n\r\n\t\t&--mini {\r\n\t\t\tpadding: $tag-mini-pd;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tborder-radius: 2px;\r\n\t\t}\r\n\r\n\t\t// type attr\r\n\t\t&--primary {\r\n\t\t\tbackground-color: $uni-primary;\r\n\t\t\tborder-color: $uni-primary;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\r\n\t\t&--success {\r\n\t\t\tcolor: #fff;\r\n\t\t\tbackground-color: $uni-success;\r\n\t\t\tborder-color: $uni-success;\r\n\t\t}\r\n\r\n\t\t&--warning {\r\n\t\t\tcolor: #fff;\r\n\t\t\tbackground-color: $uni-warning;\r\n\t\t\tborder-color: $uni-warning;\r\n\t\t}\r\n\r\n\t\t&--error {\r\n\t\t\tcolor: #fff;\r\n\t\t\tbackground-color: $uni-error;\r\n\t\t\tborder-color: $uni-error;\r\n\t\t}\r\n\r\n\t\t&--primary--inverted {\r\n\t\t\tcolor: $uni-primary;\r\n\t\t\tborder-color: $uni-primary;\r\n\t\t}\r\n\r\n\t\t&--success--inverted {\r\n\t\t\tcolor: $uni-success;\r\n\t\t\tborder-color: $uni-success;\r\n\t\t}\r\n\r\n\t\t&--warning--inverted {\r\n\t\t\tcolor: $uni-warning;\r\n\t\t\tborder-color: $uni-warning;\r\n\t\t}\r\n\r\n\t\t&--error--inverted {\r\n\t\t\tcolor: $uni-error;\r\n\t\t\tborder-color: $uni-error;\r\n\t\t}\r\n\r\n\t\t&--inverted {\r\n\t\t\tbackground-color: #fff;\r\n\t\t}\r\n\r\n\t\t// other attr\r\n\t\t&--circle {\r\n\t\t\tborder-radius: 15px;\r\n\t\t}\r\n\r\n\t\t&--mark {\r\n\t\t\tborder-top-left-radius: 0;\r\n\t\t\tborder-bottom-left-radius: 0;\r\n\t\t\tborder-top-right-radius: 15px;\r\n\t\t\tborder-bottom-right-radius: 15px;\r\n\t\t}\r\n\r\n\t\t&--disabled {\r\n\t\t\topacity: 0.5;\r\n\t\t\t/* #ifdef H5 */\r\n\t\t\tcursor: not-allowed;\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.uni-tag-text {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 14px;\r\n\r\n\t\t&--primary {\r\n\t\t\tcolor: $uni-primary;\r\n\t\t}\r\n\r\n\t\t&--success {\r\n\t\t\tcolor: $uni-success;\r\n\t\t}\r\n\r\n\t\t&--warning {\r\n\t\t\tcolor: $uni-warning;\r\n\t\t}\r\n\r\n\t\t&--error {\r\n\t\t\tcolor: $uni-error;\r\n\t\t}\r\n\r\n\t\t&--small {\r\n\t\t\tfont-size: 12px;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-tag.vue?vue&type=style&index=0&id=ea31adfc&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-tag.vue?vue&type=style&index=0&id=ea31adfc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753665199262\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}