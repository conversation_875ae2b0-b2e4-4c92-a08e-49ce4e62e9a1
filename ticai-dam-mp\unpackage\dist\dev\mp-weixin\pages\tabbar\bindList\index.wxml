<view class="container data-v-4a0277e6"><block wx:if="{{index==0}}"><view class="list-block data-v-4a0277e6"><view class="search-container data-v-4a0277e6"><view class="search-input-wrapper data-v-4a0277e6"><uni-easyinput style="height:70rpx;" vue-id="1365f440-1" type="text" placeholder="请输入姓名,账号,编码,终端号,网点备注查询" confirm-type="search" value="{{keyword}}" data-event-opts="{{[['^confirm',[['searchNo']]],['^input',[['__set_model',['','keyword','$event',[]]]]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-4a0277e6" bind:__l="__l"></uni-easyinput></view><view class="search-buttons data-v-4a0277e6"><button class="icon-btn data-v-4a0277e6" type="primary" plain="true" data-event-opts="{{[['tap',[['searchNo',['$event']]]]]}}" bindtap="__e"><uni-icons vue-id="1365f440-2" type="search" size="20" class="data-v-4a0277e6" bind:__l="__l"></uni-icons></button><button class="icon-btn data-v-4a0277e6" type="primary" plain="true" data-event-opts="{{[['tap',[['assetScan',['$event']]]]]}}" bindtap="__e"><uni-icons vue-id="1365f440-3" type="scan" size="20" class="data-v-4a0277e6" bind:__l="__l"></uni-icons></button></view></view><uni-collapse class="{{['data-v-4a0277e6',(datePickerVisible)?'date-picker-expanded':'']}}" bind:change="__e" bind:input="__e" vue-id="1365f440-4" value="{{collapseValue}}" data-event-opts="{{[['^change',[['onCollapseChange']]],['^input',[['__set_model',['','collapseValue','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><uni-collapse-item vue-id="{{('1365f440-5')+','+('1365f440-4')}}" title="{{collapseTitle}}" show-animation="{{true}}" name="filter" class="data-v-4a0277e6" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['filter-content','data-v-4a0277e6',(datePickerVisible)?'date-picker-expanded':'']}}"><view class="filter-row data-v-4a0277e6"><text class="filter-label data-v-4a0277e6">是否领用：</text><uni-data-checkbox vue-id="{{('1365f440-6')+','+('1365f440-5')}}" localdata="{{receivedOptions}}" mode="button" value="{{filterConditions.isReceived}}" data-event-opts="{{[['^change',[['onReceivedChange']]],['^input',[['__set_model',['$0','isReceived','$event',[]],['filterConditions']]]]]}}" bind:change="__e" bind:input="__e" class="data-v-4a0277e6" bind:__l="__l"></uni-data-checkbox></view><view class="filter-row data-v-4a0277e6"><text class="filter-label data-v-4a0277e6">是否绑定：</text><uni-data-checkbox vue-id="{{('1365f440-7')+','+('1365f440-5')}}" localdata="{{boundOptions}}" mode="button" value="{{filterConditions.isBound}}" data-event-opts="{{[['^change',[['onBoundChange']]],['^input',[['__set_model',['$0','isBound','$event',[]],['filterConditions']]]]]}}" bind:change="__e" bind:input="__e" class="data-v-4a0277e6" bind:__l="__l"></uni-data-checkbox></view><view class="filter-row data-v-4a0277e6"><text class="filter-label data-v-4a0277e6">是否定位：</text><uni-data-checkbox vue-id="{{('1365f440-8')+','+('1365f440-5')}}" localdata="{{locatedOptions}}" mode="button" value="{{filterConditions.isLocated}}" data-event-opts="{{[['^change',[['onLocatedChange']]],['^input',[['__set_model',['$0','isLocated','$event',[]],['filterConditions']]]]]}}" bind:change="__e" bind:input="__e" class="data-v-4a0277e6" bind:__l="__l"></uni-data-checkbox></view><view class="filter-row data-v-4a0277e6"><text class="filter-label data-v-4a0277e6">网点日期：</text><view data-event-opts="{{[['tap',[['onDatePickerClick',['$event']]]]]}}" class="date-picker-wrapper data-v-4a0277e6" bindtap="__e"><uni-datetime-picker vue-id="{{('1365f440-9')+','+('1365f440-5')}}" type="daterange" placeholder="请选择日期范围" clearIcon="{{true}}" value="{{filterConditions.dateRange}}" data-event-opts="{{[['^change',[['onDateRangeChange']]],['^input',[['__set_model',['$0','dateRange','$event',[]],['filterConditions']],['onDateRangeInput']]],['^maskClick',[['onDatePickerMaskClick']]],['^clear',[['onDateRangeClear']]],['^confirm',[['onDateRangeConfirm']]],['^cancel',[['onDateRangeCancel']]]]}}" bind:change="__e" bind:input="__e" bind:maskClick="__e" bind:clear="__e" bind:confirm="__e" bind:cancel="__e" class="data-v-4a0277e6" bind:__l="__l"></uni-datetime-picker></view></view></view></uni-collapse-item></uni-collapse><block wx:if="{{$root.g0}}"><view class="data-v-4a0277e6"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="list-item data-v-4a0277e6"><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">绑定姓名：</view><view class="text data-v-4a0277e6">{{item.$orig.name||'-'}}</view><view class="time data-v-4a0277e6">{{item.m0}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">绑定账号：</view><view class="text data-v-4a0277e6">{{item.$orig.account||'-'}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">资产编码：</view><view class="text data-v-4a0277e6">{{item.$orig.no||'-'}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">终端号：</view><view class="text data-v-4a0277e6">{{item.$orig.nowSn||'-'}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">规则型号：</view><view class="text data-v-4a0277e6">{{item.$orig.spec||'-'}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">资产类型：</view><view class="text data-v-4a0277e6">{{item.$orig.typeName||'-'}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">资产名称：</view><view class="text data-v-4a0277e6">{{item.$orig.assetName||'-'}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">业主名称：</view><view class="text data-v-4a0277e6">{{item.$orig.locationName||'-'}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">网点日期：</view><view class="text data-v-4a0277e6">{{item.m1}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">网点地址：</view><view class="text data-v-4a0277e6">{{item.$orig.locationAddress||'-'}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">网点备注：</view><view class="text data-v-4a0277e6">{{item.$orig.locationMemo||'-'}}</view></view><view class="row data-v-4a0277e6"><view class="label data-v-4a0277e6">定位地址：</view><view class="text data-v-4a0277e6">{{item.$orig.locAddr||'-'}}</view></view><view class="row row-with-divider data-v-4a0277e6"><view class="label data-v-4a0277e6">是否定位：</view><view class="text data-v-4a0277e6">{{item.$orig.whetherLocation||'-'}}</view></view></view></block></view></block><uni-load-more vue-id="1365f440-10" status="{{moreStatus}}" contentText="{{contentText}}" class="data-v-4a0277e6" bind:__l="__l"></uni-load-more></view></block></view>