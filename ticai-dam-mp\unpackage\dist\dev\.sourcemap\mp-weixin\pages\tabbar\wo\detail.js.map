{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/detail.vue?3f12", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/detail.vue?bdda", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/detail.vue?eb85", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/detail.vue?38e3", "uni-app:///pages/tabbar/wo/detail.vue", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/detail.vue?9b27", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/wo/detail.vue?33ad"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "dataModel", "imageValue", "fileList", "smsTimeCount", "smsTimer", "onLoad", "methods", "loadData", "ctx", "that", "res", "name", "extname", "url", "formatPoint"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,8UAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAA8xB,CAAgB,8yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACmFlzB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;UACA;UACAC;UACAA;UACA;YACAC;cACAD;gBAAAE;gBAAAC;gBAAAC;cAAA;cACAJ;YACA;UACA;QACA;MACA;QACAD;MACA;IACA;IACAM;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1HA;AAAA;AAAA;AAAA;AAA4nC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACAhpC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/wo/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tabbar/wo/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=fb27a020&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/wo/detail.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=fb27a020&\"", "var components\ntry {\n  components = {\n    uniCard: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-card/uni-card\" */ \"@dcloudio/uni-ui/lib/uni-card/uni-card.vue\"\n      )\n    },\n    uniFilePicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker\" */ \"@dcloudio/uni-ui/lib/uni-file-picker/uni-file-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatPoint(_vm.dataModel.servicePoint)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\t\t\t\r\n\t\t<view class=\"form-view\">\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">网点用户</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.locationName }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">网点地址</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.locationAddress }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">联系人</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.contact }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">联系电话</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.phone }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">故障反映</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.faultReport }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">现场检查</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.faultCheck }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">材料费</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.matCost }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">维修费</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.maiCost }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">合计金额</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.amount }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<uni-card v-for=\"(item,index) in dataModel.detailList\" :key=\"item.id\" :title=\"item.name\">\r\n\t\t\t<view class=\"form-view\">\r\n\t\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t\t<label class=\"label\">规格型号</label>\r\n\t\t\t\t\t<text class=\"text\">{{ item.spec }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t\t<label class=\"label\">序列号</label>\r\n\t\t\t\t\t<text class=\"text\">{{ item.sn }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t\t<label class=\"label\">故障类型</label>\r\n\t\t\t\t\t<text class=\"text\">{{ item.fault }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t\t<label class=\"label\">是否保修</label>\r\n\t\t\t\t\t<text class=\"text\">{{ item.flag == '1' ? '是' : '否' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t\t<label class=\"label\">维修情况</label>\r\n\t\t\t\t\t<text class=\"text\">{{ item.solve }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-card>\r\n\t\t<view class=\"form-view\">\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">服务评价</label>\r\n\t\t\t\t<text class=\"text\">{{ formatPoint(dataModel.servicePoint) }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">服务意见</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.serviceMemo }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"form-view-item\">\r\n\t\t\t\t<label class=\"label\">确认手机</label>\r\n\t\t\t\t<text class=\"text\">{{ dataModel.confirmMobile }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<uni-file-picker v-model=\"imageValue\" title=\"附件\" :limit=\"6\" fileMediatype=\"image\" mode=\"grid\" readonly />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport settings from '../../../utils/settings.js'\r\n\timport * as ctx from '../../../utils/context.js'\r\n\t\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdataModel: { },\r\n\t\t\t\timageValue: [],\r\n\t\t\t\tfileList: [],\r\n\t\t\t\tsmsTimeCount: 0,\r\n\t\t\t\tsmsTimer: null,\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.loadData(option.id);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tloadData(id) {\r\n\t\t\t\tconst that = this;\r\n\t\t\t\tif(id) {\r\n\t\t\t\t\tctx.post('/wx/wo/get/' + id, function(res) {\r\n\t\t\t\t\t  if(res.code < 0 || res.data == null) return ctx.error('无法获取任务信息', 'back')\r\n\t\t\t\t\t  that.dataModel = res.data;\r\n\t\t\t\t\t  that.dataModel.confirmMobile = that.dataModel.phone;\r\n\t\t\t\t\t  if(res.data.attachList) {\r\n\t\t\t\t\t\t  res.data.attachList.forEach(file => {\r\n\t\t\t\t\t\t\t  that.imageValue.push({ name: file.name, extname: file.extName, url: settings.attach_host + file.path })\r\n\t\t\t\t\t\t\t  that.fileList.push(file)\r\n\t\t\t\t\t\t  })\r\n\t\t\t\t\t  }\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tctx.error('无效参数', 'back')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tformatPoint(point) {\r\n\t\t\t\treturn ctx.getPointText(point)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.container {\r\n\t\tmargin: 12rpx;\r\n\t}\r\n\t.radio {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753665194904\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}