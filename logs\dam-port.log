07-25 17:24:26.317 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-25 17:24:26.328 [main] INFO  com.zy.dam.DamPortApplication - Starting DamPortApplication using Java 11.0.22 on bugui with PID 19172 (F:\work\ticai\ticai-dam-port\target\classes started by 22315 in F:\work\ticai\ticai-dam-port)
07-25 17:24:26.329 [main] INFO  com.zy.dam.DamPortApplication - The following 1 profile is active: "staging"
07-25 17:24:26.895 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-25 17:24:26.897 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-25 17:24:26.917 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
07-25 17:24:27.075 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=b448ecd0-1286-3770-a68b-c1c0bc253fdf
07-25 17:24:27.315 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20002 (http)
07-25 17:24:27.322 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20002"]
07-25 17:24:27.322 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-25 17:24:27.322 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-25 17:24:27.425 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/port-api] - Initializing Spring embedded WebApplicationContext
07-25 17:24:27.425 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1067 ms
07-25 17:24:27.494 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-25 17:24:27.991 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-25 17:24:29.189 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-25 17:24:29.196 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-25 17:24:29.196 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-25 17:24:29.196 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-25 17:24:29.197 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-25 17:24:29.197 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-25 17:24:29.197 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-25 17:24:29.197 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7a13ad55
07-25 17:24:29.403 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-25 17:24:29.456 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-25 17:24:29.509 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20002"]
07-25 17:24:29.528 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20002 (http) with context path '/port-api'
07-25 17:24:29.711 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-25 17:24:29.711 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-25 17:24:29.717 [main] INFO  com.zy.dam.DamPortApplication - Started DamPortApplication in 4.911 seconds (JVM running for 5.742)
07-25 17:32:30.546 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 17:32:30.778 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-25 17:32:30.778 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-25 17:32:30.778 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 17:32:30.779 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-25 17:32:30.835 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-25 17:32:31.083 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-25 17:32:36.348 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-25 17:32:36.357 [main] INFO  com.zy.dam.DamPortApplication - Starting DamPortApplication using Java 11.0.22 on bugui with PID 32724 (F:\work\ticai\ticai-dam-port\target\classes started by 22315 in F:\work\ticai\ticai-dam-port)
07-25 17:32:36.359 [main] INFO  com.zy.dam.DamPortApplication - The following 1 profile is active: "staging"
07-25 17:32:37.089 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-25 17:32:37.091 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-25 17:32:37.115 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-25 17:32:37.295 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=b448ecd0-1286-3770-a68b-c1c0bc253fdf
07-25 17:32:37.549 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20002 (http)
07-25 17:32:37.556 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20002"]
07-25 17:32:37.557 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-25 17:32:37.557 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-25 17:32:37.665 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/port-api] - Initializing Spring embedded WebApplicationContext
07-25 17:32:37.665 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1272 ms
07-25 17:32:37.733 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-25 17:32:38.254 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-25 17:32:39.672 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-25 17:32:39.679 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-25 17:32:39.679 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-25 17:32:39.679 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-25 17:32:39.680 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-25 17:32:39.680 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-25 17:32:39.680 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-25 17:32:39.680 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7a13ad55
07-25 17:32:39.930 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-25 17:32:39.990 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-25 17:32:40.036 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20002"]
07-25 17:32:40.056 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20002 (http) with context path '/port-api'
07-25 17:32:40.259 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-25 17:32:40.260 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-25 17:32:40.266 [main] INFO  com.zy.dam.DamPortApplication - Started DamPortApplication in 5.476 seconds (JVM running for 6.612)
07-25 17:38:21.368 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 17:38:21.559 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-25 17:38:21.559 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-25 17:38:21.559 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 17:38:21.559 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-25 17:38:21.594 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-25 17:38:21.845 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-25 17:38:32.577 [main] INFO  org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zy.dam.DamPortApplicationTests], using SpringBootContextLoader
07-25 17:38:32.581 [main] INFO  org.springframework.test.context.support.AbstractContextLoader - Could not detect default resource locations for test class [com.zy.dam.DamPortApplicationTests]: no resource found for suffixes {-context.xml, Context.groovy}.
07-25 17:38:32.582 [main] INFO  org.springframework.test.context.support.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.zy.dam.DamPortApplicationTests]: DamPortApplicationTests does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
07-25 17:38:32.679 [main] INFO  org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.zy.dam.DamPortApplication for test class com.zy.dam.DamPortApplicationTests
07-25 17:38:32.777 [main] INFO  org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
07-25 17:38:32.790 [main] INFO  org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@f325091, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@437e951d, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@77b325b3, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@63a5e46c, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@7e8e8651, org.springframework.test.context.support.DirtiesContextTestExecutionListener@49ef32e0, org.springframework.test.context.transaction.TransactionalTestExecutionListener@271f18d3, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@6bd51ed8, org.springframework.test.context.event.EventPublishingTestExecutionListener@61e3a1fd, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@51abf713, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@eadb475, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@4d4d48a6, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@315df4bb, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@3fc08eec, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@5cad8b7d]
07-25 17:38:34.269 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-25 17:38:34.272 [main] INFO  com.zy.dam.DamPortApplicationTests - Starting DamPortApplicationTests using Java 11.0.22 on bugui with PID 20028 (started by 22315 in F:\work\ticai\ticai-dam-port)
07-25 17:38:34.273 [main] INFO  com.zy.dam.DamPortApplicationTests - The following 1 profile is active: "staging"
07-25 17:38:34.950 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-25 17:38:34.953 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-25 17:38:34.972 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
07-25 17:38:35.191 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=ce281475-896e-352c-bde1-0d860f34422b
07-25 17:38:35.569 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-25 17:38:36.383 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-25 17:38:38.505 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-25 17:38:38.517 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-25 17:38:38.518 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-25 17:38:38.518 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-25 17:38:38.519 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-25 17:38:38.519 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-25 17:38:38.519 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-25 17:38:38.519 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@bddac1c
07-25 17:38:38.777 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-25 17:38:38.876 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-25 17:38:38.889 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-25 17:38:38.889 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-25 17:38:38.896 [main] INFO  com.zy.dam.DamPortApplicationTests - Started DamPortApplicationTests in 6.074 seconds (JVM running for 6.999)
07-25 17:38:39.728 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 17:38:39.730 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-25 17:38:39.730 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-25 17:38:39.730 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 17:38:39.730 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-25 17:38:39.747 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-25 17:38:40.030 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-25 17:38:56.294 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-25 17:38:56.488 [main] INFO  org.springframework.boot.SpringApplication - Starting application using Java 11.0.22 on bugui with PID 34280 (started by 22315 in F:\work\ticai\ticai-dam-port)
07-25 17:38:56.490 [main] INFO  org.springframework.boot.SpringApplication - The following 1 profile is active: "staging"
07-25 17:38:57.097 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-25 17:38:57.099 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-25 17:38:57.115 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
07-25 17:38:57.301 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e0c51b45-f2f7-3883-991e-ad286282c101
07-25 17:38:57.803 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20002 (http)
07-25 17:38:57.810 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20002"]
07-25 17:38:57.810 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-25 17:38:57.811 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-25 17:38:57.858 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/port-api] - Initializing Spring embedded WebApplicationContext
07-25 17:38:57.859 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1335 ms
07-25 17:38:57.953 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-25 17:38:58.420 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-25 17:39:00.250 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-25 17:39:00.261 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-25 17:39:00.262 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-25 17:39:00.263 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-25 17:39:00.264 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-25 17:39:00.264 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-25 17:39:00.265 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-25 17:39:00.265 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4d24ab25
07-25 17:39:00.511 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-25 17:39:00.567 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-25 17:39:00.579 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20002"]
07-25 17:39:00.606 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20002 (http) with context path '/port-api'
07-25 17:39:00.820 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-25 17:39:00.820 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-25 17:39:00.827 [main] INFO  org.springframework.boot.SpringApplication - Started application in 6.197 seconds (JVM running for 6.591)
07-25 17:44:56.246 [http-nio-20002-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/port-api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-25 17:44:56.247 [http-nio-20002-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-25 17:44:56.254 [http-nio-20002-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms
07-25 17:57:16.335 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 17:57:16.557 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-25 17:57:16.557 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-25 17:57:16.558 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 17:57:16.558 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-25 17:57:16.745 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-25 17:57:16.993 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
